{"rustc": 15597765236515928571, "features": "[\"calloop\", \"calloop-wayland-source\"]", "declared_features": "[\"bytemuck\", \"calloop\", \"calloop-wayland-source\", \"default\", \"pkg-config\", \"xkbcommon\"]", "target": 16218806119597165848, "profile": 15657897354478470176, "path": 14446861717789131686, "deps": [[1089140534224002486, "wayland_cursor", false, 15788610980176293967], [2924422107542798392, "libc", false, 10404782511394506294], [3430646239657634944, "rustix", false, 17145317985793182637], [5130283301485625812, "cursor_icon", false, 18232423669972034976], [5986029879202738730, "log", false, 10542980197326567410], [6279680260381740794, "wayland_csd_frame", false, 6828791016882603706], [7896293946984509699, "bitflags", false, 4167326816902153908], [8008191657135824715, "thiserror", false, 15995664439208846668], [8043219942173489582, "build_script_build", false, 15491513012893065095], [9809065382899137841, "wayland_protocols", false, 15959886824812493872], [11718880776180636413, "memmap2", false, 8849369426104933515], [11961965847686299122, "calloop_wayland_source", false, 9701209053823704888], [12089589207440818884, "calloop", false, 12927719899051815294], [13177806083327594313, "xkeysym", false, 9782842318219326497], [17079110154484429333, "wayland_protocols_wlr", false, 2205074627155724578], [17553607095298811701, "wayland_client", false, 9982959844094854989], [17916187082124099642, "wayland_backend", false, 5781353962034246524], [17920034471148732551, "wayland_scanner", false, 7499010452904356167]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/smithay-client-toolkit-90e6a01a10b0de35/dep-lib-smithay_client_toolkit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}