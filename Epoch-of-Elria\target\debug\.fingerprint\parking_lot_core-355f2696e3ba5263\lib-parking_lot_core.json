{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 947505493299811221, "profile": 15657897354478470176, "path": 8545930987007905622, "deps": [[2924422107542798392, "libc", false, 10404782511394506294], [3666196340704888985, "smallvec", false, 8429349445459453217], [10411997081178400487, "cfg_if", false, 5252366422700516714], [14196108479452351812, "instant", false, 14598608293849859715], [14814334185036658946, "build_script_build", false, 15945850721638266921]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot_core-355f2696e3ba5263/dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}