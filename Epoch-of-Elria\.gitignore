# Generated by Cargo
# will have compiled files and final executables
debug/
target/

# Remove Cargo.lock from gitignore if it should be committed
# Usually, it's good practice to commit Cargo.lock for applications
# Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds often produce these files
*.pdb
*.lib
*.dll

# IDE-specific files
.idea/
.vscode/
*.iml
*.code-workspace

# OS-specific files
.DS_Store
Thumbs.db

# Build artifacts from other languages/systems if present
*.o
*.exe
*.d
bin/
obj/
node_modules/
__pycache__/
venv/
*.pyc

# Log files
*.log

# Coverage reports
coverage/
lcov.info
*.profraw
