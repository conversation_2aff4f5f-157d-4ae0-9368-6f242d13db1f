{"rustc": 15597765236515928571, "features": "[\"client\", \"staging\", \"unstable\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"staging\", \"unstable\", \"wayland-client\", \"wayland-server\"]", "target": 7585448433277229770, "profile": 15657897354478470176, "path": 6280544202789412221, "deps": [[7896293946984509699, "bitflags", false, 4167326816902153908], [17553607095298811701, "wayland_client", false, 9982959844094854989], [17916187082124099642, "wayland_backend", false, 5781353962034246524], [17920034471148732551, "wayland_scanner", false, 7499010452904356167]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-7f6396c21c762088/dep-lib-wayland_protocols", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}