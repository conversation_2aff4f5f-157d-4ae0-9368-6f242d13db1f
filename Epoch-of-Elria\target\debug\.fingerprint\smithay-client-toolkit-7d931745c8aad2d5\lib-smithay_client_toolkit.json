{"rustc": 15597765236515928571, "features": "[\"andrew\", \"calloop\", \"default\", \"frames\"]", "declared_features": "[\"andrew\", \"calloop\", \"default\", \"frames\"]", "target": 7000747139203390044, "profile": 15657897354478470176, "path": 265126769871885649, "deps": [[3555588402607964811, "wayland_protocols", false, 3142858808607223465], [5986029879202738730, "log", false, 10542980197326567410], [6592966148407444940, "memmap2", false, 15585112350845194141], [6772218012066736839, "wayland_cursor", false, 11144226745652092733], [8109024259837965626, "andrew", false, 105546828420945881], [8289600954469699483, "wayland_client", false, 5988506572726033533], [10435729446543529114, "bitflags", false, 2283929623278890688], [10847506952746196420, "nix", false, 12224373371264336183], [13936087210852657611, "calloop", false, 6552043573139139391], [17336073172670836519, "dlib", false, 10572337770929185496], [17917672826516349275, "lazy_static", false, 7061565280469649469]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/smithay-client-toolkit-7d931745c8aad2d5/dep-lib-smithay_client_toolkit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}