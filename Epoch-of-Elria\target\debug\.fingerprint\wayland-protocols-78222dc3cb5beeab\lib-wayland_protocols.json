{"rustc": 15597765236515928571, "features": "[\"client\", \"unstable_protocols\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"unstable_protocols\", \"wayland-client\", \"wayland-server\"]", "target": 17851254929718682527, "profile": 15657897354478470176, "path": 13614612892040107871, "deps": [[3555588402607964811, "build_script_build", false, 4128677653956667059], [8289600954469699483, "wayland_client", false, 5988506572726033533], [10435729446543529114, "bitflags", false, 2283929623278890688], [11616214658452515874, "wayland_commons", false, 1750454036420801517]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-78222dc3cb5beeab/dep-lib-wayland_protocols", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}