/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/deps/libwayland_protocols-78222dc3cb5beeab.rmeta: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/fullscreen-shell-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/idle-inhibit-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-method-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-timestamps-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-dmabuf-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-explicit-synchronization-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-constraints-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-gestures-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/primary-selection-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/relative-pointer-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v2_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v3_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-decoration-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v2_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-output-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v5_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v6_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/gtk-primary-selection_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-data-control-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-export-dmabuf-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-gamma-control-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-input-inhibitor-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-layer-shell-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-power-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-screencopy-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-virtual-pointer-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/presentation-time_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/viewporter_client_api.rs

/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/deps/libwayland_protocols-78222dc3cb5beeab.rlib: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/fullscreen-shell-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/idle-inhibit-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-method-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-timestamps-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-dmabuf-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-explicit-synchronization-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-constraints-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-gestures-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/primary-selection-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/relative-pointer-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v2_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v3_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-decoration-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v2_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-output-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v5_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v6_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/gtk-primary-selection_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-data-control-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-export-dmabuf-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-gamma-control-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-input-inhibitor-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-layer-shell-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-power-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-screencopy-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-virtual-pointer-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/presentation-time_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/viewporter_client_api.rs

/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/deps/wayland_protocols-78222dc3cb5beeab.d: /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs /home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/fullscreen-shell-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/idle-inhibit-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-method-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-timestamps-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/keyboard-shortcuts-inhibit-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-dmabuf-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-explicit-synchronization-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-constraints-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-gestures-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/primary-selection-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/relative-pointer-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v2_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v3_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-decoration-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v2_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-output-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v5_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v6_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xwayland-keyboard-grab-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/gtk-primary-selection_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-data-control-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-export-dmabuf-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-foreign-toplevel-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-gamma-control-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-input-inhibitor-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-layer-shell-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-power-management-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-screencopy-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-virtual-pointer-v1_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/presentation-time_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell_client_api.rs /mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/viewporter_client_api.rs

/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/lib.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/protocol_macro.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/unstable.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/misc.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/wlr.rs:
/home/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/wayland-protocols-0.28.6/src/stable.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/fullscreen-shell-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/idle-inhibit-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-method-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/input-timestamps-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/keyboard-shortcuts-inhibit-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-dmabuf-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/linux-explicit-synchronization-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-constraints-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/pointer-gestures-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/primary-selection-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/relative-pointer-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/tablet-v2_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/text-input-v3_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-decoration-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-foreign-v2_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-output-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v5_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell-v6_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xwayland-keyboard-grab-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/gtk-primary-selection_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-data-control-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-export-dmabuf-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-foreign-toplevel-management-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-gamma-control-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-input-inhibitor-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-layer-shell-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-management-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-output-power-management-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-screencopy-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/wlr-virtual-pointer-v1_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/presentation-time_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/xdg-shell_client_api.rs:
/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out/viewporter_client_api.rs:

# env-dep:OUT_DIR=/mnt/c/Coding/Epoch-of-Elria/Epoch-of-Elria/target/debug/build/wayland-protocols-dd6a8ac915134024/out
