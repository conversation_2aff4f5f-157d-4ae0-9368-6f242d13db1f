{"rustc": 15597765236515928571, "features": "[\"default\", \"glutin_glx_sys\", \"wayland\", \"wayland-client\", \"wayland-egl\", \"x11\"]", "declared_features": "[\"default\", \"glutin_glx_sys\", \"serde\", \"wayland\", \"wayland-client\", \"wayland-egl\", \"x11\"]", "target": 8798576871209898272, "profile": 2040997289075261528, "path": 10719601187441968065, "deps": [[1365408723356066744, "winit", false, 156596191985049235], [1488740704393869182, "osmesa_sys", false, 12033480947053988625], [5986029879202738730, "log", false, 5597548263147149225], [8289600954469699483, "wayland_client", false, 17989705491646097652], [11641406201058336332, "parking_lot", false, 1389792622067799642], [11723284583626592924, "libloading", false, 10558040745816104400], [12888336062069858926, "wayland_egl", false, 6089723547281618567], [14153367844739996026, "glutin_egl_sys", false, 18373782247217643729], [17085268986702743152, "glutin_glx_sys", false, 4867983672708997346], [17917672826516349275, "lazy_static", false, 10975155677382447499]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/glutin-a98268c71dfd6cfb/dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}