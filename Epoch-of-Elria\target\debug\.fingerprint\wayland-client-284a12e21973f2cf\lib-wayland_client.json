{"rustc": 15597765236515928571, "features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "declared_features": "[\"dlopen\", \"scoped-tls\", \"use_system_lib\"]", "target": 6885752962907571681, "profile": 15657897354478470176, "path": 11516442890180196779, "deps": [[735493956294706931, "nix", false, 11705134147887302757], [2924422107542798392, "libc", false, 10404782511394506294], [8289600954469699483, "build_script_build", false, 16266109995867332484], [10435729446543529114, "bitflags", false, 2283929623278890688], [11040873101195677033, "wayland_sys", false, 10527772865316888133], [11434239582363224126, "downcast_rs", false, 16101678806360375840], [11616214658452515874, "wayland_commons", false, 1750454036420801517], [13370890382188185363, "scoped_tls", false, 12055475220260828074]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-client-284a12e21973f2cf/dep-lib-wayland_client", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}