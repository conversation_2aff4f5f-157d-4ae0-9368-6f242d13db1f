{"rustc": 15597765236515928571, "features": "[\"client\", \"unstable_protocols\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"unstable_protocols\", \"wayland-client\", \"wayland-server\"]", "target": 17851254929718682527, "profile": 2040997289075261528, "path": 13614612892040107871, "deps": [[3555588402607964811, "build_script_build", false, 1057277622341269836], [8289600954469699483, "wayland_client", false, 17989705491646097652], [10435729446543529114, "bitflags", false, 5030592503563974220], [11616214658452515874, "wayland_commons", false, 16631111414353709291]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/wayland-protocols-569fbf7dbc23b4e7/dep-lib-wayland_protocols", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}