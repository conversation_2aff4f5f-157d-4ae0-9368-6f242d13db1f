{"rustc": 15597765236515928571, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\", \"stdweb\", \"wasm-bindgen\"]", "target": 14160162848842265298, "profile": 15657897354478470176, "path": 5264903129058071498, "deps": [[8081351675046095464, "lock_api", false, 13806754096219065650], [14196108479452351812, "instant", false, 14598608293849859715], [14814334185036658946, "parking_lot_core", false, 18336025864696091467]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parking_lot-3c6352c46dcf54b6/dep-lib-parking_lot", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}