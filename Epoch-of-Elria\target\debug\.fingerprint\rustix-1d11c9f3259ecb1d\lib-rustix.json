{"rustc": 15597765236515928571, "features": "[\"alloc\", \"event\", \"fs\", \"pipe\", \"process\", \"std\", \"time\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 8475749685358207080, "path": 9707382946406477788, "deps": [[7896293946984509699, "bitflags", false, 4167326816902153908], [12053020504183902936, "build_script_build", false, 14174319510539311170], [12846346674781677812, "linux_raw_sys", false, 17954456011145926828]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-1d11c9f3259ecb1d/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}