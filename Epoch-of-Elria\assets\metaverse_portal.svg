<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for the portal -->
    <radialGradient id="portalGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#9966ff;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#6633cc;stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:#330099;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#000033;stop-opacity:0.9" />
    </radialGradient>
    
    <!-- Gradient for energy rings -->
    <radialGradient id="energyGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#ccccff;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#9966ff;stop-opacity:0.2" />
    </radialGradient>
    
    <!-- Animation for rotation -->
    <animateTransform id="rotate" attributeName="transform" type="rotate" 
                      values="0 100 100;360 100 100" dur="10s" repeatCount="indefinite"/>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="95" fill="url(#portalGradient)" stroke="#9966ff" stroke-width="2"/>
  
  <!-- Outer energy ring -->
  <circle cx="100" cy="100" r="80" fill="none" stroke="url(#energyGradient)" stroke-width="3" opacity="0.7">
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 100 100;360 100 100" dur="8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Middle energy ring -->
  <circle cx="100" cy="100" r="60" fill="none" stroke="url(#energyGradient)" stroke-width="2" opacity="0.5">
    <animateTransform attributeName="transform" type="rotate" 
                      values="360 100 100;0 100 100" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Inner energy ring -->
  <circle cx="100" cy="100" r="40" fill="none" stroke="url(#energyGradient)" stroke-width="1" opacity="0.3">
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 100 100;360 100 100" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Central core -->
  <circle cx="100" cy="100" r="20" fill="#ffffff" opacity="0.9">
    <animate attributeName="opacity" values="0.9;0.5;0.9" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Narrative symbols floating around -->
  <g opacity="0.6">
    <!-- Story symbol -->
    <text x="100" y="50" text-anchor="middle" font-family="serif" font-size="16" fill="#ccccff">📖</text>
    <!-- Music symbol -->
    <text x="150" y="100" text-anchor="middle" font-family="serif" font-size="16" fill="#ccccff">🎵</text>
    <!-- Heart symbol -->
    <text x="100" y="150" text-anchor="middle" font-family="serif" font-size="16" fill="#ccccff">💖</text>
    <!-- Liberation symbol -->
    <text x="50" y="100" text-anchor="middle" font-family="serif" font-size="16" fill="#ccccff">🔓</text>
    
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 100 100;360 100 100" dur="12s" repeatCount="indefinite"/>
  </g>
  
  <!-- Sparkle effects -->
  <g opacity="0.8">
    <circle cx="70" cy="70" r="2" fill="#ffffff">
      <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="130" cy="70" r="2" fill="#ffffff">
      <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="130" cy="130" r="2" fill="#ffffff">
      <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite" begin="1s"/>
    </circle>
    <circle cx="70" cy="130" r="2" fill="#ffffff">
      <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite" begin="1.5s"/>
    </circle>
  </g>
  
  <!-- Title text -->
  <text x="100" y="190" text-anchor="middle" font-family="serif" font-size="12" fill="#9966ff" opacity="0.8">
    Metaverse Portal
  </text>
</svg>
