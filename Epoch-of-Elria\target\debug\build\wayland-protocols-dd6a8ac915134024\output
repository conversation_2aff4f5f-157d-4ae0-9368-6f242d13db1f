cargo:rerun-if-changed-env=CARGO_FEATURE_CLIENT
cargo:rerun-if-changed-env=CARGO_FEATURE_SERVER
cargo:rerun-if-changed-env=CARGO_FEATURE_UNSTABLE_PROTOCOLS
cargo:rerun-if-changed=./protocols/stable/presentation-time/presentation-time.xml
cargo:rerun-if-changed=./protocols/stable/viewporter/viewporter.xml
cargo:rerun-if-changed=./protocols/stable/xdg-shell/xdg-shell.xml
cargo:rerun-if-changed=./misc/gtk-primary-selection.xml
cargo:rerun-if-changed=./protocols/unstable/fullscreen-shell/fullscreen-shell-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/idle-inhibit/idle-inhibit-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/input-method/input-method-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/input-timestamps/input-timestamps-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/keyboard-shortcuts-inhibit/keyboard-shortcuts-inhibit-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/linux-dmabuf/linux-dmabuf-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/linux-explicit-synchronization/linux-explicit-synchronization-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/pointer-constraints/pointer-constraints-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/pointer-gestures/pointer-gestures-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/primary-selection/primary-selection-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/relative-pointer/relative-pointer-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/tablet/tablet-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/tablet/tablet-unstable-v2.xml
cargo:rerun-if-changed=./protocols/unstable/text-input/text-input-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/text-input/text-input-unstable-v3.xml
cargo:rerun-if-changed=./protocols/unstable/xdg-decoration/xdg-decoration-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/xdg-foreign/xdg-foreign-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/xdg-foreign/xdg-foreign-unstable-v2.xml
cargo:rerun-if-changed=./protocols/unstable/xdg-output/xdg-output-unstable-v1.xml
cargo:rerun-if-changed=./protocols/unstable/xdg-shell/xdg-shell-unstable-v5.xml
cargo:rerun-if-changed=./protocols/unstable/xdg-shell/xdg-shell-unstable-v6.xml
cargo:rerun-if-changed=./protocols/unstable/xwayland-keyboard-grab/xwayland-keyboard-grab-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-data-control-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-export-dmabuf-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-foreign-toplevel-management-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-gamma-control-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-input-inhibitor-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-layer-shell-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-output-management-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-output-power-management-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-screencopy-unstable-v1.xml
cargo:rerun-if-changed=./wlr-protocols/unstable/wlr-virtual-pointer-unstable-v1.xml
