{"rustc": 15597765236515928571, "features": "[\"mio\", \"mio-extras\", \"parking_lot\", \"percent-encoding\", \"sctk\", \"wayland\", \"wayland-client\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"mio\", \"mio-extras\", \"parking_lot\", \"percent-encoding\", \"sctk\", \"serde\", \"std_web\", \"stdweb\", \"wasm-bindgen\", \"wayland\", \"wayland-client\", \"web-sys\", \"web_sys\", \"x11\", \"x11-dl\"]", "target": 5956907280325987529, "profile": 15657897354478470176, "path": 3552221278819351893, "deps": [[40386456601120721, "percent_encoding", false, 1535653983914572568], [2219490556472398059, "mio_extras", false, 11244717639413485032], [2481439302769428604, "raw_window_handle", false, 11120467941387233120], [2924422107542798392, "libc", false, 10404782511394506294], [5573101603161346839, "x11_dl", false, 8152005783313270353], [5986029879202738730, "log", false, 10542980197326567410], [8289600954469699483, "wayland_client", false, 5988506572726033533], [10435729446543529114, "bitflags", false, 2283929623278890688], [11641406201058336332, "parking_lot", false, 18224438376612147916], [14196108479452351812, "instant", false, 14598608293849859715], [15503054242377401778, "sctk", false, 15636569049156425856], [16292302275207019187, "mio", false, 9008654068680205167], [17917672826516349275, "lazy_static", false, 7061565280469649469]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winit-bf4c44872427e4e9/dep-lib-winit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}