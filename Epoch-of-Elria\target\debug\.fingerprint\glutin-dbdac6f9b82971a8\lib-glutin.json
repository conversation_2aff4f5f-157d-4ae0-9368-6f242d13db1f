{"rustc": 15597765236515928571, "features": "[\"default\", \"glutin_glx_sys\", \"wayland\", \"wayland-client\", \"wayland-egl\", \"x11\"]", "declared_features": "[\"default\", \"glutin_glx_sys\", \"serde\", \"wayland\", \"wayland-client\", \"wayland-egl\", \"x11\"]", "target": 8798576871209898272, "profile": 15657897354478470176, "path": 10719601187441968065, "deps": [[1365408723356066744, "winit", false, 11099431583628588983], [1488740704393869182, "osmesa_sys", false, 11209159583679813195], [5986029879202738730, "log", false, 10542980197326567410], [8289600954469699483, "wayland_client", false, 5988506572726033533], [11641406201058336332, "parking_lot", false, 18224438376612147916], [11723284583626592924, "libloading", false, 1512651089728664075], [12888336062069858926, "wayland_egl", false, 12890867664322522365], [14153367844739996026, "glutin_egl_sys", false, 4156760337126731614], [17085268986702743152, "glutin_glx_sys", false, 12054043466740123157], [17917672826516349275, "lazy_static", false, 7061565280469649469]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/glutin-dbdac6f9b82971a8/dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}