<!DOCTYPE html>
<html>
<head>
<title>Game Engine Output</title>
<style>
body { margin: 0; padding: 20px; background-color: #334c7f; }
svg { border: 1px solid #333; }
</style>
</head>
<body>
<h1>Game Engine - Frame Output</h1>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect x="0" y="0" width="800" height="600" fill="#334c7f"/>
  <g transform="translate(709,232.993) scale(1,1)">
    
  <polygon points="10,2 16,8 14,18 6,18 4,8" fill="#0066FF" stroke="#0044CC" stroke-width="1"/>
  <polygon points="10,2 12,6 8,6" fill="white" opacity="0.6"/>

  </g>
  <g transform="translate(321,336.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(168,72.9933) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(625,381.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(619,67.9933) scale(1,1)">
    
  <polygon points="10,2 16,8 14,18 6,18 4,8" fill="#0066FF" stroke="#0044CC" stroke-width="1"/>
  <polygon points="10,2 12,6 8,6" fill="white" opacity="0.6"/>

  </g>
  <g transform="translate(234,405.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(74,239.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(88,248.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(726,397.993) scale(1,1)">
    
  <polygon points="10,2 16,8 14,18 6,18 4,8" fill="#0066FF" stroke="#0044CC" stroke-width="1"/>
  <polygon points="10,2 12,6 8,6" fill="white" opacity="0.6"/>

  </g>
  <g transform="translate(172,508.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(106,263.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(444,111.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(167,511.993) scale(1,1)">
    
  <polygon points="10,2 16,8 14,18 6,18 4,8" fill="#0066FF" stroke="#0044CC" stroke-width="1"/>
  <polygon points="10,2 12,6 8,6" fill="white" opacity="0.6"/>

  </g>
  <g transform="translate(698,142.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(468,330.993) scale(1,1)">
    
  <circle cx="10" cy="10" r="8" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  <circle cx="10" cy="10" r="5" fill="none" stroke="#FFA500" stroke-width="1"/>
  <text x="10" y="12" text-anchor="middle" font-family="serif" font-size="8" fill="#B8860B">$</text>

  </g>
  <g transform="translate(400,300) scale(1,1)">
    
  <rect x="10" y="15" width="30" height="25" fill="#4169E1" rx="5"/>
  <circle cx="25" cy="10" r="7.5" fill="#FFE4B5"/>
  <circle cx="22.5" cy="9" r="2" fill="black"/>
  <circle cx="27.5" cy="9" r="2" fill="black"/>
  <rect x="5" y="20" width="7.5" height="15" fill="#FFE4B5" rx="3"/>
  <rect x="37.5" y="20" width="7.5" height="15" fill="#FFE4B5" rx="3"/>
  <rect x="15" y="37.5" width="7.5" height="10" fill="#4169E1" rx="3"/>
  <rect x="27.5" y="37.5" width="7.5" height="10" fill="#4169E1" rx="3"/>

  </g>
  <text x="10" y="30" font-family="monospace" font-size="20" fill="#ffffff" fill-opacity="1">Score: 0</text>
  <text x="10" y="60" font-family="monospace" font-size="16" fill="#ffffff" fill-opacity="1">Items: 15</text>
</svg>
<p>Frame rendered with 18 draw calls.</p>
</body>
</html>