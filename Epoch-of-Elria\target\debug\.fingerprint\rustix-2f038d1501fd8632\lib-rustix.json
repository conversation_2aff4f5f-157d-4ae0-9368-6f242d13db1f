{"rustc": 15597765236515928571, "features": "[\"alloc\", \"default\", \"event\", \"fs\", \"libc-extra-traits\", \"net\", \"pipe\", \"process\", \"shm\", \"std\", \"system\", \"thread\", \"use-libc-auxv\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 8582226208830198931, "path": 10093631420466920670, "deps": [[3430646239657634944, "build_script_build", false, 6331256181561661794], [5036304442846774733, "linux_raw_sys", false, 3762357257354512071], [7896293946984509699, "bitflags", false, 4167326816902153908]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-2f038d1501fd8632/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}