{"rustc": 15597765236515928571, "features": "[]", "declared_features": "[\"async-task\", \"block_on\", \"executor\", \"futures-io\", \"nightly_coverage\", \"nix\", \"pin-utils\", \"signals\"]", "target": 13000572321397389619, "profile": 15657897354478470176, "path": 3604988136271045076, "deps": [[3430646239657634944, "rustix", false, 17145317985793182637], [5151680991372207887, "polling", false, 5742302372486921719], [5986029879202738730, "log", false, 10542980197326567410], [6955678925937229351, "slab", false, 4782610552428104062], [7896293946984509699, "bitflags", false, 4167326816902153908], [8008191657135824715, "thiserror", false, 15995664439208846668]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/calloop-773fe89c8b927f51/dep-lib-calloop", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}