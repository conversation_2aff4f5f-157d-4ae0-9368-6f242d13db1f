{"rustc": 15597765236515928571, "features": "[\"client\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"wayland-client\", \"wayland-server\"]", "target": 16532630540568316278, "profile": 15657897354478470176, "path": 11860975606991206728, "deps": [[7896293946984509699, "bitflags", false, 4167326816902153908], [9809065382899137841, "wayland_protocols", false, 15959886824812493872], [17553607095298811701, "wayland_client", false, 9982959844094854989], [17916187082124099642, "wayland_backend", false, 5781353962034246524], [17920034471148732551, "wayland_scanner", false, 7499010452904356167]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-wlr-a7a93a23847fcbf2/dep-lib-wayland_protocols_wlr", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}