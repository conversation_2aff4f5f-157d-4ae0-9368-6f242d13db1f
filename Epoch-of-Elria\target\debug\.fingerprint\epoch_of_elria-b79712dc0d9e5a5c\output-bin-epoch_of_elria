{"$message_type":"diagnostic","message":"unused imports: `ControlFlow`, `Event<PERSON>oop`, `Event`, `WindowEvent`, and `window::WindowBuilder`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/main.rs","byte_start":3909,"byte_end":3914,"line_start":98,"line_end":98,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"    event::{Event, WindowEvent},","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/main.rs","byte_start":3916,"byte_end":3927,"line_start":98,"line_end":98,"column_start":20,"column_end":31,"is_primary":true,"text":[{"text":"    event::{Event, WindowEvent},","highlight_start":20,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/main.rs","byte_start":3948,"byte_end":3959,"line_start":99,"line_end":99,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    event_loop::{ControlFlow, EventLoop},","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/main.rs","byte_start":3961,"byte_end":3970,"line_start":99,"line_end":99,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"    event_loop::{ControlFlow, EventLoop},","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/main.rs","byte_start":3978,"byte_end":3999,"line_start":100,"line_end":100,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    window::WindowBuilder,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":3883,"byte_end":4006,"line_start":97,"line_end":102,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use winit::{","highlight_start":1,"highlight_end":13},{"text":"    event::{Event, WindowEvent},","highlight_start":1,"highlight_end":33},{"text":"    event_loop::{ControlFlow, EventLoop},","highlight_start":1,"highlight_end":42},{"text":"    window::WindowBuilder,","highlight_start":1,"highlight_end":27},{"text":"};","highlight_start":1,"highlight_end":3},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ControlFlow`, `EventLoop`, `Event`, `WindowEvent`, and `window::WindowBuilder`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:98:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    event::{Event, WindowEvent},\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m99\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    event_loop::{ControlFlow, EventLoop},\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    window::WindowBuilder,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/main.rs","byte_start":4891,"byte_end":4910,"line_start":123,"line_end":123,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::U) { // 'U' to upgrade first gold mine","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `is_key_pressed` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":4891,"byte_end":4910,"line_start":123,"line_end":123,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::U) { // 'U' to upgrade first gold mine","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":"is_key_pressed","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:123:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   if input.is_key_pressed_once(epoch_of_elria::input::Key::U) { // 'U' to\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `is_key_pressed` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;9mis_key_pressed_once\u001b[0m\u001b[0m(epoch_of_elria::input::Key::U) { // 'U' to upgrade first gold mine\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;10mis_key_pressed\u001b[0m\u001b[0m(epoch_of_elria::input::Key::U) { // 'U' to upgrade first gold mine\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/main.rs","byte_start":5276,"byte_end":5295,"line_start":129,"line_end":129,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::I) { // 'I' to upgrade first dust extractor","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `is_key_pressed` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":5276,"byte_end":5295,"line_start":129,"line_end":129,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::I) { // 'I' to upgrade first dust extractor","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":"is_key_pressed","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:129:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   if input.is_key_pressed_once(epoch_of_elria::input::Key::I) { // 'I' to\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `is_key_pressed` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;9mis_key_pressed_once\u001b[0m\u001b[0m(epoch_of_elria::input::Key::I) { // 'I' to upgrade first dust extractor\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;10mis_key_pressed\u001b[0m\u001b[0m(epoch_of_elria::input::Key::I) { // 'I' to upgrade first dust extractor\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/main.rs","byte_start":5689,"byte_end":5708,"line_start":135,"line_end":135,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::X) { // 'X' to gain 50 XP","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `is_key_pressed` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":5689,"byte_end":5708,"line_start":135,"line_end":135,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::X) { // 'X' to gain 50 XP","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":"is_key_pressed","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:135:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   if input.is_key_pressed_once(epoch_of_elria::input::Key::X) { // 'X' to\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `is_key_pressed` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;9mis_key_pressed_once\u001b[0m\u001b[0m(epoch_of_elria::input::Key::X) { // 'X' to gain 50 XP\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;10mis_key_pressed\u001b[0m\u001b[0m(epoch_of_elria::input::Key::X) { // 'X' to gain 50 XP\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/main.rs","byte_start":5891,"byte_end":5910,"line_start":139,"line_end":139,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::R) { // 'R' to reset epoch","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `is_key_pressed` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":5891,"byte_end":5910,"line_start":139,"line_end":139,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::R) { // 'R' to reset epoch","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":"is_key_pressed","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:139:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   if input.is_key_pressed_once(epoch_of_elria::input::Key::R) { // 'R' to\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `is_key_pressed` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;9mis_key_pressed_once\u001b[0m\u001b[0m(epoch_of_elria::input::Key::R) { // 'R' to reset epoch\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m139\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;10mis_key_pressed\u001b[0m\u001b[0m(epoch_of_elria::input::Key::R) { // 'R' to reset epoch\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/main.rs","byte_start":6102,"byte_end":6121,"line_start":143,"line_end":143,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::B) { // 'B' to apply a test bonus","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a method `is_key_pressed` with a similar name","code":null,"level":"help","spans":[{"file_name":"src/main.rs","byte_start":6102,"byte_end":6121,"line_start":143,"line_end":143,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"        if input.is_key_pressed_once(epoch_of_elria::input::Key::B) { // 'B' to apply a test bonus","highlight_start":18,"highlight_end":37}],"label":null,"suggested_replacement":"is_key_pressed","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `is_key_pressed_once` found for reference `&InputManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/main.rs:143:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   if input.is_key_pressed_once(epoch_of_elria::input::Key::B) { // 'B' to\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a method `is_key_pressed` with a similar name\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;9mis_key_pressed_once\u001b[0m\u001b[0m(epoch_of_elria::input::Key::B) { // 'B' to apply a test bonus\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        if input.\u001b[0m\u001b[0m\u001b[38;5;10mis_key_pressed\u001b[0m\u001b[0m(epoch_of_elria::input::Key::B) { // 'B' to apply a test bonus\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 5 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 5 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0599`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0599`.\u001b[0m\n"}
