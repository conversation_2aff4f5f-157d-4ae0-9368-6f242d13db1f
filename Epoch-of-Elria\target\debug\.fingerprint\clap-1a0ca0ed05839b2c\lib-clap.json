{"rustc": 15597765236515928571, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15221872889701672926, "path": 14914236958872914704, "deps": [[4925398738524877221, "clap_derive", false, 12761003136646821360], [14814905555676593471, "clap_builder", false, 2445776445525343921]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-1a0ca0ed05839b2c/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}