{"rustc": 15597765236515928571, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"abomonation\", \"abomonation-serialize\", \"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam013\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-mint\", \"cuda\", \"cust\", \"debug\", \"default\", \"glam013\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rkyv\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 15657897354478470176, "path": 1532665113599211048, "deps": [[2819946551904607991, "num_rational", false, 10915663622568480661], [5157631553186200874, "num_traits", false, 15004916726349192008], [12319020793864570031, "num_complex", false, 16389376130895818203], [15677050387741058262, "approx", false, 222313202938884251], [15826188163127377936, "matrixmultiply", false, 13340361550059621509], [17001665395952474378, "typenum", false, 8561776250945559807], [17514367198935401919, "nalgebra_macros", false, 3922205116333238521], [17877237321909315803, "simba", false, 426362796643549140]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nalgebra-3414a55165e8b4b3/dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}